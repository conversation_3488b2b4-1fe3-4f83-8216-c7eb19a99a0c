<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://gist.github.com https://s.imgur.com https://www.youtube.com https://player.vimeo.com https://apis.google.com https://accounts.google.com https://*.googleapis.com https://www.gstatic.com https://*.firebaseapp.com https://*.firebase.app;
      frame-src 'self' https://www.youtube.com https://player.vimeo.com https://drive.google.com https://docs.google.com https://codepen.io https://jsfiddle.net https://replit.com https://www.figma.com https://www.loom.com https://accounts.google.com https://www.google.com https://*.firebaseapp.com https://*.firebase.app;
      img-src 'self' https://i.imgur.com https://img.youtube.com https://i.vimeocdn.com https://res.cloudinary.com https://lh3.googleusercontent.com https://*.googleusercontent.com https://ui-avatars.com https://www.google.com https://*.google.com data: blob:;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://www.gstatic.com;
      style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://www.gstatic.com;
      connect-src 'self' https://firestore.googleapis.com https://*.firestore.googleapis.com https://api.cloudinary.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://www.googleapis.com https://accounts.google.com https://*.googleapis.com https://www.google.com https://*.firebaseapp.com https://*.firebase.app;
      font-src 'self' data: https://fonts.gstatic.com;
    ">
    <!-- Content Security Policy for TradeYa with specific allowances for embedded content -->
    <title>TradeYa - Trade Skills and Services</title>
    <!-- Preconnect to important domains -->
    <link rel="preconnect" href="https://firestore.googleapis.com">
    <link rel="preconnect" href="https://res.cloudinary.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Outfit:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://apis.google.com">
    <link rel="preconnect" href="https://accounts.google.com">
    <link rel="preconnect" href="https://www.gstatic.com">
    <link rel="preconnect" href="https://tradeya-45ede.firebaseapp.com">
  </head>
  <body>
    <div id="root"></div>
    <!-- Fallback content in case JavaScript fails to load -->
    <noscript>
      <div style="padding: 20px; text-align: center;">
        <h1>JavaScript Required</h1>
        <p>Please enable JavaScript to use TradeYa.</p>
      </div>
    </noscript>
    <!-- Debug info that will be hidden when the app loads -->
    <div id="loading-debug" style="padding: 20px; display: none;">
      <h2>Loading TradeYa...</h2>
      <p>If you see this message for more than a few seconds, there might be an issue with the application.</p>
      <p>Try refreshing the page or checking your internet connection.</p>
    </div>
    <script>
      // Show loading debug info
      document.getElementById('loading-debug').style.display = 'block';

      // Hide loading debug info when the app loads
      window.addEventListener('load', function() {
        document.getElementById('loading-debug').style.display = 'none';
      });
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

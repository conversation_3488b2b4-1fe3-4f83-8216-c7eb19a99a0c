[build]
  command = "npm run build:force"
  publish = "dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    Cross-Origin-Opener-Policy = "same-origin-allow-popups"
    Cross-Origin-Embedder-Policy = "credentialless"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.google.com https://*.googleapis.com; connect-src 'self' wss://*.firebaseio.com https://*.googleapis.com https://firestore.googleapis.com; img-src 'self' data: blob: https://*.googleusercontent.com; frame-src 'self' https://*.google.com;"

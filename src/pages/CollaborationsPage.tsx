import React, { useState, useEffect } from 'react';
import { getCollaborations, Collaboration, getUserProfile, User } from '../services/firestore';
import { useToast } from '../contexts/ToastContext';
import { PlusCircle } from '../utils/icons';
import { themeClasses } from '../utils/themeUtils';
import ProfileImageWithUser from '../components/ui/ProfileImageWithUser';
import PerformanceMonitor from '../components/ui/PerformanceMonitor';
import { Link, useNavigate } from 'react-router-dom';
import { cn } from '../utils/cn';

export const CollaborationsPage: React.FC = () => {
  const { addToast } = useToast();
  const navigate = useNavigate();

  const [collaborations, setCollaborations] = useState<Collaboration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [collaborationCreators, setCollaborationCreators] = useState<{[key: string]: User}>({});

  useEffect(() => {
    fetchCollaborations();
  }, []);

  const fetchCollaborationCreators = async (creatorIds: string[]) => {
    try {
      const creators: {[key: string]: User} = {};

      for (const creatorId of creatorIds) {
        if (creatorId) {
          const { data, error } = await getUserProfile(creatorId);
          if (!error && data) {
            creators[creatorId] = data as User;
          }
        }
      }

      setCollaborationCreators(creators);
    } catch (err) {
      console.error('Error fetching collaboration creators:', err);
    }
  };

  const fetchCollaborations = async () => {
    setLoading(true);
    setError(null);

    try {
      const collaborationsResult = await getCollaborations();
      if (collaborationsResult.error) throw new Error(collaborationsResult.error.message);
      if (collaborationsResult.data) {
        console.log('Fetched collaborations:', collaborationsResult.data);
        console.log('Number of collaborations:', collaborationsResult.data.length);
        setCollaborations(collaborationsResult.data);

        const creatorIds = collaborationsResult.data
          .map((collab: Collaboration) => collab.creatorId)
          .filter((id: string | undefined, index: number, self: (string | undefined)[]) => id && self.indexOf(id) === index);

        if (creatorIds.length > 0) {
          fetchCollaborationCreators(creatorIds as string[]);
        }
      } else {
        setCollaborations([]);
        console.log('No collaborations found');
      }
    } catch (err: any) {
      console.error('Error fetching collaborations:', err);
      setError(err.message || 'Failed to fetch collaborations');
      addToast('error', err.message || 'Failed to fetch collaborations');
    } finally {
      setLoading(false);
    }
  };



  return (
    <>
      {/* Performance monitoring (invisible) */}
      <PerformanceMonitor pageName="CollaborationsPage" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className={`text-3xl font-bold ${themeClasses.text}`}>Collaborations</h1>
            <p className={`mt-1 text-sm ${themeClasses.textMuted}`}>
              Find or create collaborative projects with other creative professionals
            </p>
          </div>

        <div className="mt-4 md:mt-0 flex space-x-3">
          <button
            onClick={() => navigate('/collaborations/new')}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Collaboration
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 dark:border-orange-400"></div>
        </div>
      ) : collaborations.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {collaborations.map((collab) => (
            <div key={collab.id} className={cn(`overflow-hidden rounded-lg ${themeClasses.border} ${themeClasses.card} shadow-sm p-5 ${themeClasses.transition}`, themeClasses.hoverCard)}>
              <h3 className={`text-lg font-semibold ${themeClasses.text}`}>{collab.title}</h3>

              {collab.creatorId && collab.creatorName && (
                <div className="mt-2 flex items-center">
                  {collab.creatorId === 'TozfQg0dAHe4ToLyiSnkDqe3ECj2' ? (
                    <img
                      src="https://res.cloudinary.com/doqqhj2nt/image/upload/c_fill,g_face,h_400,w_400,q_auto:best,f_auto/v1737789591/profile-pictures/TozfQg0dAHe4ToLyiSnkDqe3ECj2_47251d4b-f5a6-42b3-a7de-dcdeb2f66543.jpg"
                      alt="John Roberts"
                      className="h-6 w-6 rounded-full mr-2"
                    />
                  ) : (
                    <ProfileImageWithUser
                      userId={collab.creatorId}
                      profileUrl={collaborationCreators[collab.creatorId]?.profilePicture || collaborationCreators[collab.creatorId]?.photoURL}
                      size="xs"
                      className="h-6 w-6 rounded-full mr-2"
                    />
                  )}
                  <span className={`text-sm ${themeClasses.textMuted}`}>
                    {collaborationCreators[collab.creatorId]?.displayName || collab.creatorName || 'Unknown User'}
                  </span>
                </div>
              )}

              <p className={`mt-2 text-sm ${themeClasses.textMuted} line-clamp-2`}>{collab.description}</p>

              <div className="mt-4">
                <h4 className={`text-sm font-medium ${themeClasses.text} mb-2`}>Roles Needed:</h4>
                <div className="flex flex-wrap gap-2">
                  {collab.roles && collab.roles.map((role, index) => (
                    <span
                      key={index}
                      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${role.filled ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'} transition-colors duration-200`}
                    >
                      {role.title} {role.filled ? '(Filled)' : '(Open)'}
                    </span>
                  ))}
                </div>
              </div>

              <div className="mt-4 flex justify-between items-center">
                <span className={`text-xs ${themeClasses.textMuted}`}>
                  {collab.createdAt && collab.createdAt.seconds ?
                    new Date(collab.createdAt.seconds * 1000).toLocaleDateString() :
                    'Recently added'}
                </span>
                <span className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/30 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-300 transition-colors duration-200">
                  {collab.status || 'Active'}
                </span>
              </div>

              <div className="mt-4">
                <Link
                  to={`/collaborations/${collab.id}`}
                  className={`w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${themeClasses.primaryButton} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 ${themeClasses.transition}`}
                >
                  View Collaboration
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className={cn(`${themeClasses.card} p-12 rounded-lg shadow-sm ${themeClasses.border} text-center ${themeClasses.transition}`, themeClasses.hoverCard)}>
          <h3 className={`text-lg font-medium ${themeClasses.text} mb-2`}>No collaborations found</h3>
          <p className={`${themeClasses.textMuted} mb-6`}>
            There are currently no active collaborations.
          </p>
        </div>
      )}
    </div>
    </>
  );
};

export default CollaborationsPage;

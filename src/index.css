@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Outfit:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base typography */
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Light mode */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-card: #ffffff;
  --color-text-primary: #1f2937;
  --color-text-secondary: #4b5563;
  --color-border: #e5e7eb;
  --color-shadow: rgba(0, 0, 0, 0.05);

  /* Brand colors */
  --color-primary: #f97316;
  --color-primary-hover: #ea580c;
  --color-secondary: #0ea5e9;
  --color-accent: #8b5cf6;

  color-scheme: light dark;
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dark {
  /* Dark mode */
  --color-bg-primary: #111827;
  --color-bg-secondary: #1f2937;
  --color-bg-card: #1f2937;
  --color-text-primary: #f9fafb;
  --color-text-secondary: #d1d5db;
  --color-border: #374151;
  --color-shadow: rgba(0, 0, 0, 0.3);

  /* Adjusted brand colors for dark mode */
  --color-primary: #fb923c;
  --color-primary-hover: #f97316;
  --color-secondary: #38bdf8;
  --color-accent: #a78bfa;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

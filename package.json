{"scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "test": "jest --coverage", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --reporters='default' --reporters='jest-junit'", "security:init": "./scripts/set-permissions.sh", "security:validate": "./scripts/validate-security.sh", "security:test": "jest --config jest.config.security.js", "security:rules": "jest --testMatch='**/__tests__/firebase-security.test.ts' --runInBand", "security:deploy": "./scripts/deploy-security-rules.sh", "security:check": "./scripts/check-security-rules.sh", "security:audit": "npm audit --production && npm run security:validate", "security:report": "./scripts/validate-security.sh --report-only", "prepare": "npm run security:init && husky install", "precommit": "lint-staged && npm run security:check", "firebase:emulators": "firebase emulators:start --only firestore,storage", "firebase:deploy:rules": "npm run security:validate && npm run security:deploy", "firebase:rules:lint": "firebase firestore:rules:lint firestore.rules && firebase storage:rules:lint storage.rules", "ci:security": "npm run security:validate && npm run security:test", "ci:full": "npm run type-check && npm run lint && npm run test:ci && npm run ci:security", "analyze": "source-map-explorer 'dist/assets/*.js'", "scan": "npm audit && npm outdated", "validate": "npm run type-check && npm run lint && npm run test && npm run security:validate"}, "dependencies": {"@types/node": "^20.2.5", "firebase": "^9.22.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.2", "zod": "^3.21.4"}, "devDependencies": {"@firebase/rules-unit-testing": "^3.0.0", "@firebase/testing": "^0.20.11", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.1", "@types/node": "^20.2.5", "@types/react": "^18.2.7", "@types/react-dom": "^18.2.4", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.41.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "eslint-plugin-security": "^1.7.1", "eslint-plugin-sonarjs": "^0.19.0", "firebase-admin": "^11.9.0", "gitleaks": "^8.16.3", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^13.2.2", "prettier": "^2.8.8", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "vite": "^4.3.9", "vite-plugin-checker": "^0.6.0", "vite-tsconfig-paths": "^4.2.0"}}
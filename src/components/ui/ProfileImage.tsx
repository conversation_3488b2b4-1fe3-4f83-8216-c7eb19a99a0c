import React, { useState, useMemo, useCallback } from 'react';
import { getProfileImageUrl, generateAvatarUrl } from '../../utils/imageUtils';
import { themeClasses } from '../../utils/themeUtils';
import ErrorBoundary from './ErrorBoundary';
import { useToast } from '../../contexts/ToastContext';
import LazyImage from './LazyImage';

export interface ProfileImageProps {
  photoURL?: string | null;
  profilePicture?: string | null;
  displayName?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  onClick?: () => void;
}

// Size classes - moved outside component to prevent recreation on each render
const sizeClasses = {
  xs: 'h-8 w-8',
  sm: 'h-10 w-10',
  md: 'h-12 w-12',
  lg: 'h-16 w-16',
  xl: 'h-24 w-24'
};

/**
 * Image component with error handling
 */
const ImageWithFallback = React.memo(({
  src,
  alt,
  className,
  onClick,
  onError
}: {
  src: string;
  alt: string;
  className: string;
  onClick?: () => void;
  onError: () => void;
}) => {
  return (
    <LazyImage
      src={src}
      alt={alt}
      className={className}
      onClick={onClick}
      onError={onError}
      loading="lazy"
      decoding="async"
      fetchpriority="auto"
    />
  );
});

/**
 * ProfileImage component
 *
 * A standardized component for displaying user profile images with proper fallbacks
 * and error handling
 *
 * Performance optimized with:
 * - React.memo to prevent unnecessary re-renders
 * - useMemo for computed values
 * - useCallback for functions
 * - Conditional console logs (only in development)
 */
export const ProfileImage = React.memo<ProfileImageProps>(({
  photoURL,
  profilePicture,
  displayName = 'User',
  size = 'md',
  className = '',
  onClick
}) => {
  const { addToast } = useToast();
  const [useDefaultAvatar, setUseDefaultAvatar] = useState(false);
  const isDev = process.env.NODE_ENV === 'development';

  // Common classes - memoized to prevent string concatenation on every render
  const imageClasses = useMemo(() =>
    `rounded-full object-cover ${sizeClasses[size]} ${className} ${themeClasses.transition} border border-gray-200 dark:border-gray-700`,
    [size, className]
  );

  // Special handling for direct Cloudinary URLs - memoized to prevent recalculation
  const primaryImageUrl = useMemo(() => {
    if (photoURL?.includes('cloudinary.com') && photoURL?.includes('/c_fill')) {
      if (isDev) {
        console.debug('Using direct Cloudinary URL with transformations:', photoURL);
      }
      return photoURL;
    } else if (profilePicture?.includes('cloudinary.com') && profilePicture?.includes('/c_fill')) {
      if (isDev) {
        console.debug('Using direct profile picture URL with transformations:', profilePicture);
      }
      return profilePicture;
    } else {
      // Standard processing
      return profilePicture 
  ? getProfileImageUrl(profilePicture)
  : getProfileImageUrl(photoURL || generateAvatarUrl(displayName));
    }
  }, [photoURL, profilePicture, displayName, isDev]);

  // Handle image load error - memoized to maintain referential equality
  const handleImageError = useCallback(() => {
    // Check if we're already using a generated avatar URL
    const isGeneratedAvatar = primaryImageUrl?.includes('ui-avatars.com');

    // Only log as error and show toast if it's not a generated avatar
    if (!isGeneratedAvatar) {
      console.warn('Failed to load profile image, falling back to avatar');
      addToast('info', 'Using default avatar');
    } else if (isDev) {
      // For generated avatars, just log as info since this is expected
      console.info('Using generated avatar for user:', displayName);
    }

    setUseDefaultAvatar(true);
  }, [primaryImageUrl, addToast, displayName, isDev]);

  // Get image URLs - only log in development
  if (isDev) {
    console.debug('ProfileImage component rendering with:', {
      photoURL,
      profilePicture,
      displayName
    });
  }

  // Generate fallback avatar URL - memoized to prevent recalculation
  const fallbackAvatarUrl = useMemo(() =>
    generateAvatarUrl(displayName),
    [displayName]
  );

  if (isDev) {
    console.debug('ProfileImage using URL:', primaryImageUrl);
  }

  // Fallback UI for error boundary - memoized to prevent recreation
  const fallbackUI = useMemo(() => (
    <LazyImage
      src={fallbackAvatarUrl}
      alt={displayName}
      className={imageClasses}
      onClick={onClick}
      loading="lazy"
      decoding="async"
    />
  ), [fallbackAvatarUrl, displayName, imageClasses, onClick]);

  return (
    <ErrorBoundary fallback={fallbackUI}>
      <ImageWithFallback
        src={useDefaultAvatar ? fallbackAvatarUrl : primaryImageUrl}
        alt={displayName}
        className={imageClasses}
        onClick={onClick}
        onError={handleImageError}
      />
    </ErrorBoundary>
  );
});

// Export both named and default export for backward compatibility
export { ProfileImage as default };

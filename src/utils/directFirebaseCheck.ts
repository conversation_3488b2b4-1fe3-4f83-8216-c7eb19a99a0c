import { db } from '../firebase-config';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';

/**
 * Utility function to directly check Firebase data
 */
export const checkFirebaseData = async () => {
  try {
    console.log('Starting direct Firebase check...');
    
    // Check conversation
    const conversationId = 'bcB1UuJ2VHwTXsTFG71g';
    console.log(`Checking conversation: ${conversationId}`);
    
    // Check messages in nested collection
    const messagesPath = `conversations/${conversationId}/messages`;
    console.log(`Checking messages at path: ${messagesPath}`);
    
    const messagesRef = collection(db, 'conversations', conversationId, 'messages');
    const q = query(messagesRef, orderBy('createdAt', 'asc'));
    
    const querySnapshot = await getDocs(q);
    console.log(`Found ${querySnapshot.size} messages in nested collection`);
    
    if (querySnapshot.empty) {
      console.log('No messages found in nested collection');
    } else {
      querySnapshot.forEach((doc) => {
        console.log(`Message ${doc.id}:`, doc.data());
      });
    }
    
    return {
      success: true,
      messageCount: querySnapshot.size,
      messages: querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
    };
  } catch (error) {
    console.error('Error in direct Firebase check:', error);
    return {
      success: false,
      error
    };
  }
};

// Export a function to run the check
export const runDirectCheck = () => {
  console.log('Running direct Firebase check...');
  checkFirebaseData()
    .then(result => {
      console.log('Direct check result:', result);
    })
    .catch(error => {
      console.error('Direct check error:', error);
    });
};

// Run the check automatically when this module is imported
runDirectCheck();

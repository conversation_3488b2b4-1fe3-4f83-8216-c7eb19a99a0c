{"version": 2, "public": true, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cross-Origin-Opener-Policy", "value": "same-origin-allow-popups"}, {"key": "Cross-Origin-Embedder-Policy", "value": "credentialless"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.google.com https://*.googleapis.com; connect-src 'self' wss://*.firebaseio.com https://*.googleapis.com https://firestore.googleapis.com; img-src 'self' data: blob: https://*.googleusercontent.com; frame-src 'self' https://*.google.com;"}]}], "buildCommand": "npm run vercel-build", "outputDirectory": "dist", "framework": "vite", "github": {"silent": true}}
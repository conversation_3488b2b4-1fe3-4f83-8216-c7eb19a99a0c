// src/components/features/portfolio/PortfolioTab.tsx

import React, { useEffect, useState, useMemo } from 'react';
import { getUserPortfolioItems } from '../../../services/portfolio';
import { PortfolioItem } from '../../../types/portfolio';
import PortfolioItemComponent from './PortfolioItem';

interface PortfolioTabProps {
  userId: string;
  isOwnProfile: boolean;
}

export const PortfolioTab: React.FC<PortfolioTabProps> = ({ userId, isOwnProfile }) => {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filter, setFilter] = useState<'all' | 'trades' | 'collaborations' | 'featured'>('all');
  const [loading, setLoading] = useState(false);
  const [isManaging, setIsManaging] = useState(false);

  const fetchPortfolio = async () => {
    setLoading(true);
    const options = !isOwnProfile ? { onlyVisible: true } : {};
    const items = await getUserPortfolioItems(userId, options);
    setPortfolioItems(items);
    setLoading(false);
  };

  useEffect(() => {
    fetchPortfolio();
    // eslint-disable-next-line
  }, [userId, isOwnProfile]);

  const filteredItems = useMemo(() => {
    switch (filter) {
      case 'trades':
        return portfolioItems.filter(item => item.sourceType === 'trade');
      case 'collaborations':
        return portfolioItems.filter(item => item.sourceType === 'collaboration');
      case 'featured':
        return portfolioItems.filter(item => item.featured);
      default:
        return portfolioItems;
    }
  }, [portfolioItems, filter]);

  return (
    <div className="portfolio-container">
      <div className="portfolio-header flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Portfolio ({portfolioItems.length})</h2>
        <div className="flex items-center space-x-2">
          <button
            className={`px-2 py-1 rounded ${viewMode === 'grid' ? 'bg-orange-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setViewMode('grid')}
          >
            Grid
          </button>
          <button
            className={`px-2 py-1 rounded ${viewMode === 'list' ? 'bg-orange-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setViewMode('list')}
          >
            List
          </button>
          <select
            value={filter}
            onChange={e => setFilter(e.target.value as any)}
            className="ml-2 border rounded px-2 py-1"
          >
            <option value="all">All</option>
            <option value="trades">Trades</option>
            <option value="collaborations">Collaborations</option>
            <option value="featured">Featured</option>
          </select>
          {isOwnProfile && (
            <button
              className={`ml-2 px-2 py-1 rounded ${isManaging ? 'bg-orange-500 text-white' : 'bg-gray-200'}`}
              onClick={() => setIsManaging(v => !v)}
            >
              {isManaging ? 'Done' : 'Manage'}
            </button>
          )}
        </div>
      </div>
      {loading ? (
        <div className="py-8 text-center text-gray-500">Loading portfolio...</div>
      ) : filteredItems.length === 0 ? (
        <div className="py-8 text-center text-gray-500">No portfolio items to display.</div>
      ) : (
        <div className={`portfolio-items ${viewMode} grid gap-4`}>
          {filteredItems.map(item => (
            <PortfolioItemComponent
              key={item.id}
              item={item}
              isOwnProfile={isOwnProfile}
              isManaging={isManaging}
              onChange={fetchPortfolio}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default PortfolioTab;

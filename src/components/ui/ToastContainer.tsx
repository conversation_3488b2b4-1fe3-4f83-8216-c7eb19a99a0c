import React, { useState, createContext, useContext } from 'react';
import { createPortal } from 'react-dom';
import { Toast, ToastType } from './Toast';
import { TransitionGroup } from './transitions/TransitionGroup';

// Toast item interface
interface ToastItem {
  id: string;
  type: ToastType;
  message: string;
  duration?: number;
}

// Toast context interface
interface ToastContextType {
  addToast: (type: ToastType, message: string, duration?: number) => void;
  removeToast: (id: string) => void;
  removeAllToasts: () => void;
}

// Create context
const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Toast provider
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  // Add toast
  const addToast = (type: ToastType, message: string, duration = 5000) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    setToasts(prevToasts => [...prevToasts, { id, type, message, duration }]);
  };

  // Remove toast
  const removeToast = (id: string) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  };

  // Remove all toasts
  const removeAllToasts = () => {
    setToasts([]);
  };

  // Context value
  const contextValue: ToastContextType = {
    addToast,
    removeToast,
    removeAllToasts
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </ToastContext.Provider>
  );
};

// Hook to use toast
export const useToast = () => {
  const context = useContext(ToastContext);

  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }

  return context;
};

// Toast container component
interface ToastContainerProps {
  toasts: ToastItem[];
  removeToast: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  removeToast,
  position = 'top-right'
}) => {
  // Position classes
  const positionClasses = {
    'top-right': 'top-0 right-0',
    'top-left': 'top-0 left-0',
    'bottom-right': 'bottom-0 right-0',
    'bottom-left': 'bottom-0 left-0'
  };

  // Create portal to render toasts at the end of the document body
  return createPortal(
    <div
      className={`fixed ${positionClasses[position]} z-50 p-4 w-full max-w-sm`}
      aria-live="assertive"
    >
      <TransitionGroup>
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            type={toast.type}
            message={toast.message}
            duration={toast.duration}
            onClose={() => removeToast(toast.id)}
          />
        ))}
      </TransitionGroup>
    </div>,
    document.body
  );
};
